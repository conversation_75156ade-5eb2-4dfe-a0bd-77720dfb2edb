export const nationalityOptions = [
  { value: 'Afghanistan', label: 'Afghanistan' },
  { value: 'Albania', label: 'Albania' },
  { value: 'Algeria', label: 'Algeria' },
  { value: 'Argentina', label: 'Argentina' },
  { value: 'Australia', label: 'Australia' },
  { value: 'Austria', label: 'Austria' },
  { value: 'Bahrain', label: 'Bahrain' },
  { value: 'Bangladesh', label: 'Bangladesh' },
  { value: 'Belgium', label: 'Belgium' },
  { value: 'Brazil', label: 'Brazil' },
  { value: 'Canada', label: 'Canada' },
  { value: 'China', label: 'China' },
  { value: 'Denmark', label: 'Denmark' },
  { value: 'Egypt', label: 'Egypt' },
  { value: 'Finland', label: 'Finland' },
  { value: 'France', label: 'France' },
  { value: 'Germany', label: 'Germany' },
  { value: 'Ghana', label: 'Ghana' },
  { value: 'Greece', label: 'Greece' },
  { value: 'India', label: 'India' },
  { value: 'Indonesia', label: 'Indonesia' },
  { value: 'Iran', label: 'Iran' },
  { value: 'Iraq', label: 'Iraq' },
  { value: 'Ireland', label: 'Ireland' },
  { value: 'Italy', label: 'Italy' },
  { value: 'Japan', label: 'Japan' },
  { value: 'Jordan', label: 'Jordan' },
  { value: 'Kuwait', label: 'Kuwait' },
  { value: 'Lebanon', label: 'Lebanon' },
  { value: 'Malaysia', label: 'Malaysia' },
  { value: 'Morocco', label: 'Morocco' },
  { value: 'Netherlands', label: 'Netherlands' },
  { value: 'Nigeria', label: 'Nigeria' },
  { value: 'Norway', label: 'Norway' },
  { value: 'Oman', label: 'Oman' },
  { value: 'Pakistan', label: 'Pakistan' },
  { value: 'Palestine', label: 'Palestine' },
  { value: 'Philippines', label: 'Philippines' },
  { value: 'Poland', label: 'Poland' },
  { value: 'Qatar', label: 'Qatar' },
  { value: 'Russia', label: 'Russia' },
  { value: 'Saudi Arabia', label: 'Saudi Arabia' },
  { value: 'Singapore', label: 'Singapore' },
  { value: 'South Africa', label: 'South Africa' },
  { value: 'South Korea', label: 'South Korea' },
  { value: 'Spain', label: 'Spain' },
  { value: 'Sri Lanka', label: 'Sri Lanka' },
  { value: 'Sudan', label: 'Sudan' },
  { value: 'Sweden', label: 'Sweden' },
  { value: 'Switzerland', label: 'Switzerland' },
  { value: 'Syria', label: 'Syria' },
  { value: 'Thailand', label: 'Thailand' },
  { value: 'Tunisia', label: 'Tunisia' },
  { value: 'Turkey', label: 'Turkey' },
  { value: 'UAE', label: 'United Arab Emirates' },
  { value: 'United Kingdom', label: 'United Kingdom' },
  { value: 'United States', label: 'United States' },
  { value: 'Yemen', label: 'Yemen' },
  { value: 'Other', label: 'Other' }
];

export const educationLevelOptions = [
  { value: 'High School', label: 'High School' },
  { value: 'Diploma', label: 'Diploma' },
  { value: 'Bachelor', label: 'Bachelor' },
  { value: 'Master', label: 'Master' },
  { value: 'PHD', label: 'PHD' }
];

export const experienceYearsOptions = [
  { value: 'Fresh Graduate', label: 'Fresh Graduate' },
  { value: 'Less than 1 year', label: 'Less than 1 year' },
  { value: '1-2 years', label: '1-2 years' },
  { value: '2-3 years', label: '2-3 years' },
  { value: '3-5 years', label: '3-5 years' },
  { value: '5-7 years', label: '5-7 years' },
  { value: '7-10 years', label: '7-10 years' },
  { value: '10-15 years', label: '10-15 years' },
  { value: 'More than 15 years', label: 'More than 15 years' }
];

export const languageOptions = [
  { value: 'Arabic', label: 'Arabic' },
  { value: 'English', label: 'English' },
  { value: 'French', label: 'French' },
  { value: 'Spanish', label: 'Spanish' },
  { value: 'German', label: 'German' },
  { value: 'Chinese', label: 'Chinese' },
  { value: 'Arabic, English', label: 'Arabic, English' },
  { value: 'Arabic, English, French', label: 'Arabic, English, French' },
  { value: 'English, French', label: 'English, French' },
  { value: 'English, Spanish', label: 'English, Spanish' },
  { value: 'Other', label: 'Other' }
];

export const howHeardOptions = [
  { value: 'Career Fair', label: 'Career Fair' },
  { value: 'Networking Event', label: 'Networking Event' },
  { value: 'Job Board', label: 'Job Board (e.g., LinkedIn, Indeed, Glassdoor)' },
  { value: 'Company Website', label: 'Company Website' },
  { value: 'Employee Referral', label: 'Employee Referral' },
  { value: 'Social Media', label: 'Social Media (e.g., Facebook, Twitter, Instagram)' },
  { value: 'Recruitment Agency', label: 'Recruitment Agency' },
  { value: 'Walk-in', label: 'Walk-in / Direct Application' },
  { value: 'University Career Center', label: 'University Career Center' },
  { value: 'Professional Network', label: 'Professional Network' },
  { value: 'Online Advertisement', label: 'Online Advertisement' },
  { value: 'Other', label: 'Other' }
];

export const genderOptions = [
  { value: 'Male', label: 'Male' },
  { value: 'Female', label: 'Female' }
];

// Generate graduation years (last 30 years)
export const graduationYearOptions = Array.from({ length: 30 }, (_, i) => {
  const year = new Date().getFullYear() - i;
  return { value: year.toString(), label: year.toString() };
});
