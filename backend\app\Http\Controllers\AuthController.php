<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    /**
     * Admin login
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Simple hardcoded admin credentials for demo
        // In production, you should use proper user authentication
        $adminUsername = 'admin';
        $adminPassword = 'admin123';

        if ($request->username === $adminUsername && $request->password === $adminPassword) {
            // Generate a simple token (in production, use Laravel Sanctum or JWT)
            $token = base64_encode($adminUsername . ':' . time());

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'token' => $token,
                'user' => [
                    'username' => $adminUsername,
                    'role' => 'admin'
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid credentials'
        ], 401);
    }

    /**
     * Admin logout
     */
    public function logout(Request $request)
    {
        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Verify token
     */
    public function verify(Request $request)
    {
        $token = $request->bearerToken();

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'No token provided'
            ], 401);
        }

        // Simple token verification (in production, use proper token validation)
        try {
            $decoded = base64_decode($token);
            if (strpos($decoded, 'admin:') === 0) {
                return response()->json([
                    'success' => true,
                    'user' => [
                        'username' => 'admin',
                        'role' => 'admin'
                    ]
                ]);
            }
        } catch (\Exception $e) {
            // Invalid token
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid token'
        ], 401);
    }
}
