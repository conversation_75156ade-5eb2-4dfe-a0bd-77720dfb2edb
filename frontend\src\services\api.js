import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Applicant API functions
export const applicantAPI = {
  // Get all applicants with optional filters
  getAll: (filters = {}) => {
    const params = new URLSearchParams();
    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params.append(key, filters[key]);
      }
    });
    return api.get(`/applicants?${params.toString()}`);
  },

  // Get single applicant
  getById: (id) => api.get(`/applicants/${id}`),

  // Create new applicant
  create: (formData) => {
    return api.post('/applicants', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Update applicant
  update: (id, data) => api.put(`/applicants/${id}`, data),

  // Delete applicant
  delete: (id) => api.delete(`/applicants/${id}`),

  // Export applicants to Excel
  export: (filters = {}) => {
    const params = new URLSearchParams();
    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params.append(key, filters[key]);
      }
    });
    return api.get(`/applicants-export?${params.toString()}`, {
      responseType: 'blob',
    });
  },
};

export default api;
