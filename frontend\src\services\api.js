import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('admin_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle authentication errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
      // Redirect to login if on admin page
      if (window.location.pathname.includes('/admin')) {
        window.location.href = '/admin/login';
      }
    }
    return Promise.reject(error);
  }
);

// Applicant API functions
export const applicantAPI = {
  // Get all applicants with optional filters
  getAll: (filters = {}) => {
    const params = new URLSearchParams();
    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params.append(key, filters[key]);
      }
    });
    return api.get(`/applicants?${params.toString()}`);
  },

  // Get single applicant
  getById: (id) => api.get(`/applicants/${id}`),

  // Create new applicant
  create: (formData) => {
    return api.post('/applicants', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Update applicant
  update: (id, data) => api.put(`/applicants/${id}`, data),

  // Delete applicant
  delete: (id) => api.delete(`/applicants/${id}`),

  // Export applicants to Excel
  export: (filters = {}) => {
    const params = new URLSearchParams();
    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params.append(key, filters[key]);
      }
    });
    return api.get(`/applicants-export?${params.toString()}`, {
      responseType: 'blob',
    });
  },
};

// Authentication API functions
export const authAPI = {
  // Admin login
  login: (credentials) => api.post('/auth/login', credentials),

  // Admin logout
  logout: () => api.post('/auth/logout'),

  // Verify token
  verify: () => api.get('/auth/verify'),

  // Check if user is authenticated
  isAuthenticated: () => {
    const token = localStorage.getItem('admin_token');
    return !!token;
  },

  // Get current user
  getCurrentUser: () => {
    const user = localStorage.getItem('admin_user');
    return user ? JSON.parse(user) : null;
  },

  // Set authentication data
  setAuth: (token, user) => {
    localStorage.setItem('admin_token', token);
    localStorage.setItem('admin_user', JSON.stringify(user));
  },

  // Clear authentication data
  clearAuth: () => {
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
  }
};

export default api;
