import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navigation from './components/Navigation';
import ApplicantForm from './components/ApplicantForm';
import AdminDashboard from './components/AdminDashboard';
import AdminLogin from './components/AdminLogin';
import ProtectedRoute from './components/ProtectedRoute';
import './App.css';

function App() {
  return (
    <Router>
      <div className="min-h-screen">
        <Routes>
          <Route path="/" element={<ApplicantForm />} />
          <Route path="/admin/login" element={
            <div className="min-h-screen bg-gray-100">
              <Navigation />
              <AdminLogin />
            </div>
          } />
          <Route
            path="/admin"
            element={
              <div className="min-h-screen bg-gray-100">
                <Navigation />
                <ProtectedRoute>
                  <AdminDashboard />
                </ProtectedRoute>
              </div>
            }
          />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
