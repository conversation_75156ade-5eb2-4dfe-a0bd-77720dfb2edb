<?php

use App\Http\Controllers\ApplicantController;
use App\Http\Controllers\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Authentication routes
Route::post('auth/login', [AuthController::class, 'login']);
Route::post('auth/logout', [AuthController::class, 'logout']);
Route::get('auth/verify', [AuthController::class, 'verify']);

// Public applicant routes (for form submission)
Route::post('applicants', [ApplicantController::class, 'store']);

// Protected admin routes
Route::middleware('admin.auth')->group(function () {
    Route::get('applicants', [ApplicantController::class, 'index']);
    Route::get('applicants/{id}', [ApplicantController::class, 'show']);
    Route::put('applicants/{id}', [ApplicantController::class, 'update']);
    Route::delete('applicants/{id}', [ApplicantController::class, 'destroy']);
    Route::get('applicants-export', [ApplicantController::class, 'export']);
});
