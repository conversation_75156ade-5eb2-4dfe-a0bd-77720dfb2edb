<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Applicant extends Model
{
    protected $fillable = [
        'name',
        'gender',
        'nationality',
        'birth_date',
        'email',
        'phone',
        'education_level',
        'university',
        'major',
        'graduation_year',
        'gpa',
        'experience_years',
        'last_position',
        'last_employer',
        'languages',
        'how_heard',
        'cv_path',
        'message'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
