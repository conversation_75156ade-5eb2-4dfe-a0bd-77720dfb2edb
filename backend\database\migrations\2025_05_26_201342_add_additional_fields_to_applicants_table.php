<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applicants', function (Blueprint $table) {
            $table->string('gender')->nullable()->after('name');
            $table->string('nationality')->nullable()->after('gender');
            $table->date('birth_date')->nullable()->after('nationality');
            $table->string('education_level')->nullable()->after('phone');
            $table->string('university')->nullable()->after('education_level');
            $table->string('major')->nullable()->after('university');
            $table->string('graduation_year')->nullable()->after('major');
            $table->string('gpa')->nullable()->after('graduation_year');
            $table->string('experience_years')->nullable()->after('gpa');
            $table->string('last_position')->nullable()->after('experience_years');
            $table->string('last_employer')->nullable()->after('last_position');
            $table->string('languages')->nullable()->after('last_employer');
            $table->string('how_heard')->nullable()->after('languages');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applicants', function (Blueprint $table) {
            $table->dropColumn([
                'gender',
                'nationality',
                'birth_date',
                'education_level',
                'university',
                'major',
                'graduation_year',
                'gpa',
                'experience_years',
                'last_position',
                'last_employer',
                'languages',
                'how_heard'
            ]);
        });
    }
};
