# Applicant Management System

A full-stack web application built with <PERSON><PERSON> (backend) and <PERSON><PERSON> (frontend) for managing job applications.

## Features

### Frontend (React)
- **Applicant Form**: Submit job applications with personal information and CV upload
- **Admin Dashboard**: View, filter, search, and export applicant data
- **Responsive Design**: Works on desktop and mobile devices
- **File Upload**: Secure CV/resume upload with validation
- **Real-time Validation**: Client-side and server-side form validation

### Backend (Laravel)
- **RESTful API**: Clean API endpoints for all operations
- **File Storage**: Secure file upload and storage
- **Data Filtering**: Advanced filtering and search capabilities
- **Excel Export**: Export filtered applicant data to Excel files with clickable CV links
- **Database**: MySQL database with proper migrations
- **CORS Support**: Configured for frontend-backend communication

## Technology Stack

### Backend
- **Laravel 12** (latest version)
- **MySQL** database
- **Fast Excel** package for Excel export
- **File storage** with <PERSON><PERSON>'s storage system

### Frontend
- **React 18** with functional components and hooks
- **Vite** for fast development and building
- **React Router** for navigation
- **Axios** for API communication
- **Tailwind CSS v3.4** for styling and responsive design

## Project Structure

```
TTE/
├── backend/                 # Laravel API
│   ├── app/
│   │   ├── Http/Controllers/
│   │   │   └── ApplicantController.php
│   │   └── Models/
│   │       └── Applicant.php
│   ├── database/migrations/
│   ├── routes/api.php
│   └── config/
├── frontend/                # React App
│   ├── src/
│   │   ├── components/
│   │   │   ├── ApplicantForm.jsx
│   │   │   ├── AdminDashboard.jsx
│   │   │   └── Navigation.jsx
│   │   ├── services/
│   │   │   └── api.js
│   │   └── App.jsx
│   └── package.json
└── README.md
```

## Setup Instructions

### Prerequisites
- PHP 8.2 or higher
- Composer
- Node.js 18 or higher
- MySQL
- Git

### Backend Setup (Laravel)

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Environment configuration**
   - Copy `.env.example` to `.env`
   - Update database configuration in `.env`:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=applicant_management
   DB_USERNAME=root
   DB_PASSWORD=your_password
   ```

4. **Generate application key**
   ```bash
   php artisan key:generate
   ```

5. **Create database**
   ```sql
   CREATE DATABASE applicant_management;
   ```

6. **Run migrations**
   ```bash
   php artisan migrate
   ```

7. **Create storage link**
   ```bash
   php artisan storage:link
   ```

8. **Start the server**
   ```bash
   php artisan serve
   ```
   The API will be available at `http://localhost:8000`

### Frontend Setup (React)

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```
   The frontend will be available at `http://localhost:5173`

## API Endpoints

### Authentication
- `POST /api/auth/login` - Admin login
- `POST /api/auth/logout` - Admin logout
- `GET /api/auth/verify` - Verify authentication token

### Applicants
- `POST /api/applicants` - Create new applicant (public)
- `GET /api/applicants` - Get all applicants (admin only)
- `GET /api/applicants/{id}` - Get specific applicant (admin only)
- `PUT /api/applicants/{id}` - Update applicant (admin only)
- `DELETE /api/applicants/{id}` - Delete applicant (admin only)
- `GET /api/applicants-export` - Export applicants to Excel (admin only)

### Query Parameters for Filtering
- `name` - Filter by name (partial match)
- `email` - Filter by email (partial match)
- `date_from` - Filter by application date (from)
- `date_to` - Filter by application date (to)
- `page` - Pagination page number

## Usage

### For Applicants
1. Visit the homepage (`http://localhost:5173`)
2. Fill out the comprehensive application form with:

   **Personal Information:**
   - Full name
   - Gender
   - Nationality
   - Birth date
   - Email address
   - Mobile number

   **Education:**
   - Educational qualification
   - University name
   - Academic major
   - Graduation year
   - GPA

   **Experience:**
   - Total years of experience
   - Last position title (optional)
   - Last employer name (optional)

   **Additional Information:**
   - Languages
   - How did you hear about us
   - CV/Resume file (PDF, DOC, DOCX - max 2MB)
   - Cover letter/message (optional)

3. Submit the application

### For Administrators
1. Visit the admin login page (`http://localhost:5173/admin/login`)
2. Login with credentials:
   - **Username**: `admin`
   - **Password**: `admin123`
3. Access the admin dashboard to:
   - View all submitted applications
   - Use filters to search by name, email, or date range
   - Export filtered results to Excel
   - Delete applications if needed
4. Logout when finished

## File Upload

- **Supported formats**: PDF, DOC, DOCX
- **Maximum size**: 2MB
- **Storage location**: `storage/app/public/cvs/`
- **Access URL**: `http://localhost:8000/storage/cvs/{filename}`

## Excel Export Features

The Excel export functionality includes the following columns:
- **ID**: Applicant unique identifier
- **Name**: Full name of the applicant
- **Gender**: Gender selection
- **Nationality**: Nationality selection
- **Birth Date**: Date of birth
- **Email**: Email address
- **Phone**: Mobile number
- **Education Level**: Educational qualification
- **University**: University name
- **Major**: Academic major
- **Graduation Year**: Year of graduation
- **GPA**: Grade Point Average
- **Experience Years**: Total years of experience
- **Last Position**: Last position title
- **Last Employer**: Last employer name
- **Languages**: Languages spoken
- **How Heard**: How they heard about the company
- **CV Link**: Direct clickable link to the uploaded CV file
- **Message**: Cover letter or message from applicant
- **Applied Date**: Date and time of application submission

**Note**: CV links in the Excel file are clickable and will open the CV file directly in the browser.

## Development

### Adding New Features
1. **Backend**: Add new routes in `routes/api.php` and corresponding controller methods
2. **Frontend**: Create new components in `src/components/` and add routes in `App.jsx`

### Database Changes
1. Create new migration: `php artisan make:migration migration_name`
2. Update models as needed
3. Run migration: `php artisan migrate`

## Production Deployment

### Backend
1. Set `APP_ENV=production` in `.env`
2. Set `APP_DEBUG=false` in `.env`
3. Configure proper database credentials
4. Run `composer install --optimize-autoloader --no-dev`
5. Run `php artisan config:cache`
6. Run `php artisan route:cache`
7. Set up proper web server (Apache/Nginx)

### Frontend
1. Update API base URL in `src/services/api.js`
2. Run `npm run build`
3. Deploy the `dist/` folder to your web server

## Security Considerations

- File upload validation (type and size)
- CORS configuration for API access
- Input validation and sanitization
- SQL injection prevention through Eloquent ORM
- XSS protection through React's built-in escaping

## License

This project is open-source and available under the MIT License.
