import { useState } from 'react';
import { Link } from 'react-router-dom';
import { applicantAPI, authAPI } from '../services/api';

const ApplicantForm = () => {
  const [formData, setFormData] = useState({
    // Personal Information
    name: '',
    gender: '',
    nationality: '',
    birthDate: '',
    email: '',
    phone: '',

    // Education
    educationLevel: '',
    university: '',
    major: '',
    graduationYear: '',
    gpa: '',

    // Experience
    experienceYears: '',
    lastPosition: '',
    lastEmployer: '',

    // Additional
    languages: '',
    howHeard: '',
    message: ''
  });
  const [cvFile, setCvFile] = useState(null);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setCvFile(file);
    if (errors.cv) {
      setErrors(prev => ({
        ...prev,
        cv: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Personal Information
    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required';
    }
    if (!formData.gender) {
      newErrors.gender = 'Gender is required';
    }
    if (!formData.nationality) {
      newErrors.nationality = 'Nationality is required';
    }
    if (!formData.birthDate) {
      newErrors.birthDate = 'Birth date is required';
    }
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!formData.phone.trim()) {
      newErrors.phone = 'Mobile number is required';
    }

    // Education
    if (!formData.educationLevel) {
      newErrors.educationLevel = 'Educational qualification is required';
    }
    if (!formData.university.trim()) {
      newErrors.university = 'University name is required';
    }
    if (!formData.major.trim()) {
      newErrors.major = 'Academic major is required';
    }
    if (!formData.graduationYear) {
      newErrors.graduationYear = 'Graduation year is required';
    }
    if (!formData.gpa.trim()) {
      newErrors.gpa = 'GPA is required';
    }

    // Experience
    if (!formData.experienceYears) {
      newErrors.experienceYears = 'Years of experience is required';
    }

    // Additional
    if (!formData.languages) {
      newErrors.languages = 'Languages is required';
    }
    if (!formData.howHeard) {
      newErrors.howHeard = 'How did you hear about us is required';
    }

    // CV File
    if (!cvFile) {
      newErrors.cv = 'CV file is required';
    } else if (!['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(cvFile.type)) {
      newErrors.cv = 'CV must be a PDF, DOC, or DOCX file';
    } else if (cvFile.size > 2 * 1024 * 1024) {
      newErrors.cv = 'CV file size must be less than 2MB';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      const submitData = new FormData();
      submitData.append('name', formData.name);
      submitData.append('email', formData.email);
      submitData.append('phone', formData.phone);
      submitData.append('message', formData.message);
      submitData.append('cv', cvFile);

      const response = await applicantAPI.create(submitData);

      if (response.data.success) {
        setSubmitMessage('Application submitted successfully!');
        setShowSuccess(true);
        // Reset form
        setFormData({
          // Personal Information
          name: '',
          gender: '',
          nationality: '',
          birthDate: '',
          email: '',
          phone: '',

          // Education
          educationLevel: '',
          university: '',
          major: '',
          graduationYear: '',
          gpa: '',

          // Experience
          experienceYears: '',
          lastPosition: '',
          lastEmployer: '',

          // Additional
          languages: '',
          howHeard: '',
          message: ''
        });
        setCvFile(null);
        // Reset file input
        document.getElementById('cv').value = '';
        // Hide success message after 5 seconds
        setTimeout(() => {
          setShowSuccess(false);
          setSubmitMessage('');
        }, 5000);
      }
    } catch (error) {
      if (error.response?.data?.errors) {
        setErrors(error.response.data.errors);
      } else {
        setSubmitMessage('Failed to submit application. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-lg mb-4">
                <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6.294a2 2 0 01-1.255 1.855l-7.49 2.997a2 2 0 01-1.51 0l-7.49-2.997A2 2 0 013 14.294V8a2 2 0 012-2V6" />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Job Application
              </h1>
              <h2 className="text-lg font-semibold text-blue-600 mb-4">
                APPLICANT MANAGEMENT SYSTEM
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Welcome to our job application form. Please complete all of the following fields accurately and clearly.
                The information you provide will be used solely for recruitment purposes.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Form Section */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="px-6 py-8 sm:px-8">
            <div className="mb-8">
              <p className="text-sm text-gray-600 mb-6">
                Please enable JavaScript in your browser to complete this form.
              </p>
            </div>

            {/* Success Message */}
            {showSuccess && (
              <div className="mb-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-green-800">Application Submitted Successfully!</h3>
                    <p className="text-green-700 mt-1">Thank you for your interest. We'll review your application and get back to you soon.</p>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {submitMessage && !showSuccess && (
              <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-xl">
                <div className="flex items-center">
                  <svg className="h-5 w-5 text-red-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-red-700">{submitMessage}</p>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information Section */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                  Personal Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Full Name */}
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.name ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter your full name"
                    />
                    {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                  </div>

                  {/* Gender */}
                  <div>
                    <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-2">
                      Gender *
                    </label>
                    <select
                      id="gender"
                      name="gender"
                      value={formData.gender}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.gender ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">---- Select ----</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                    </select>
                    {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender}</p>}
                  </div>

                  {/* Nationality */}
                  <div>
                    <label htmlFor="nationality" className="block text-sm font-medium text-gray-700 mb-2">
                      Nationality *
                    </label>
                    <select
                      id="nationality"
                      name="nationality"
                      value={formData.nationality}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.nationality ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">---- Select ----</option>
                      <option value="Saudi Arabia">Saudi Arabia</option>
                      <option value="United States">United States</option>
                      <option value="United Kingdom">United Kingdom</option>
                      <option value="Canada">Canada</option>
                      <option value="Australia">Australia</option>
                      <option value="Germany">Germany</option>
                      <option value="France">France</option>
                      <option value="India">India</option>
                      <option value="Pakistan">Pakistan</option>
                      <option value="Bangladesh">Bangladesh</option>
                      <option value="Egypt">Egypt</option>
                      <option value="Jordan">Jordan</option>
                      <option value="Lebanon">Lebanon</option>
                      <option value="Syria">Syria</option>
                      <option value="Other">Other</option>
                    </select>
                    {errors.nationality && <p className="text-red-500 text-sm mt-1">{errors.nationality}</p>}
                  </div>

                  {/* Birth Date */}
                  <div>
                    <label htmlFor="birthDate" className="block text-sm font-medium text-gray-700 mb-2">
                      Birth Date *
                    </label>
                    <input
                      type="date"
                      id="birthDate"
                      name="birthDate"
                      value={formData.birthDate}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.birthDate ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                    {errors.birthDate && <p className="text-red-500 text-sm mt-1">{errors.birthDate}</p>}
                  </div>

                  {/* Email */}
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.email ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter your email address"
                    />
                    {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                  </div>

                  {/* Mobile Number */}
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                      Mobile Number *
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.phone ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter your mobile number"
                    />
                    {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
                  </div>
                </div>
              </div>

              {/* Education Section */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                  Education
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Educational Qualification */}
                  <div>
                    <label htmlFor="educationLevel" className="block text-sm font-medium text-gray-700 mb-2">
                      Educational Qualification *
                    </label>
                    <select
                      id="educationLevel"
                      name="educationLevel"
                      value={formData.educationLevel}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.educationLevel ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">---- Select ----</option>
                      <option value="High School">High School</option>
                      <option value="Diploma">Diploma</option>
                      <option value="Bachelor">Bachelor</option>
                      <option value="Master">Master</option>
                      <option value="PHD">PHD</option>
                    </select>
                    {errors.educationLevel && <p className="text-red-500 text-sm mt-1">{errors.educationLevel}</p>}
                  </div>

                  {/* University Name */}
                  <div>
                    <label htmlFor="university" className="block text-sm font-medium text-gray-700 mb-2">
                      University Name *
                    </label>
                    <input
                      type="text"
                      id="university"
                      name="university"
                      value={formData.university}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.university ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter university name"
                    />
                    {errors.university && <p className="text-red-500 text-sm mt-1">{errors.university}</p>}
                  </div>

                  {/* Academic Major */}
                  <div>
                    <label htmlFor="major" className="block text-sm font-medium text-gray-700 mb-2">
                      Academic Major *
                    </label>
                    <input
                      type="text"
                      id="major"
                      name="major"
                      value={formData.major}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.major ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter your major"
                    />
                    {errors.major && <p className="text-red-500 text-sm mt-1">{errors.major}</p>}
                  </div>

                  {/* Graduation Year */}
                  <div>
                    <label htmlFor="graduationYear" className="block text-sm font-medium text-gray-700 mb-2">
                      Graduation Year *
                    </label>
                    <select
                      id="graduationYear"
                      name="graduationYear"
                      value={formData.graduationYear}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.graduationYear ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">---- Select ----</option>
                      {Array.from({ length: 30 }, (_, i) => {
                        const year = new Date().getFullYear() - i;
                        return (
                          <option key={year} value={year}>
                            {year}
                          </option>
                        );
                      })}
                    </select>
                    {errors.graduationYear && <p className="text-red-500 text-sm mt-1">{errors.graduationYear}</p>}
                  </div>

                  {/* GPA */}
                  <div>
                    <label htmlFor="gpa" className="block text-sm font-medium text-gray-700 mb-2">
                      GPA *
                    </label>
                    <input
                      type="text"
                      id="gpa"
                      name="gpa"
                      value={formData.gpa}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.gpa ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter your GPA"
                    />
                    {errors.gpa && <p className="text-red-500 text-sm mt-1">{errors.gpa}</p>}
                  </div>
                </div>
              </div>

              {/* Experience Section */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                  Experience
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Total Years of Experience */}
                  <div>
                    <label htmlFor="experienceYears" className="block text-sm font-medium text-gray-700 mb-2">
                      Total Years of Experience *
                    </label>
                    <select
                      id="experienceYears"
                      name="experienceYears"
                      value={formData.experienceYears}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.experienceYears ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">---- Select ----</option>
                      <option value="Fresh Graduate">Fresh Graduate</option>
                      <option value="< 1 year">< 1 year</option>
                      <option value="1-2 years">1-2 years</option>
                      <option value="2-3 years">2-3 years</option>
                      <option value="3-5 years">3-5 years</option>
                      <option value="5-7 years">5-7 years</option>
                      <option value="7-10 years">7-10 years</option>
                      <option value="10-15 years">10-15 years</option>
                      <option value="> 15 years">> 15 years</option>
                    </select>
                    {errors.experienceYears && <p className="text-red-500 text-sm mt-1">{errors.experienceYears}</p>}
                  </div>

                  {/* Last Position Title */}
                  <div>
                    <label htmlFor="lastPosition" className="block text-sm font-medium text-gray-700 mb-2">
                      Last Position Title
                    </label>
                    <input
                      type="text"
                      id="lastPosition"
                      name="lastPosition"
                      value={formData.lastPosition}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter your last position"
                    />
                  </div>

                  {/* Last Employer Name */}
                  <div>
                    <label htmlFor="lastEmployer" className="block text-sm font-medium text-gray-700 mb-2">
                      Last Employer Name
                    </label>
                    <input
                      type="text"
                      id="lastEmployer"
                      name="lastEmployer"
                      value={formData.lastEmployer}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter your last employer"
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information Section */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                  Additional Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Languages */}
                  <div>
                    <label htmlFor="languages" className="block text-sm font-medium text-gray-700 mb-2">
                      Languages *
                    </label>
                    <select
                      id="languages"
                      name="languages"
                      value={formData.languages}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.languages ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">---- Select ----</option>
                      <option value="Arabic">Arabic</option>
                      <option value="English">English</option>
                      <option value="French">French</option>
                      <option value="Arabic, English">Arabic, English</option>
                      <option value="Arabic, English, French">Arabic, English, French</option>
                      <option value="Other">Other</option>
                    </select>
                    {errors.languages && <p className="text-red-500 text-sm mt-1">{errors.languages}</p>}
                  </div>

                  {/* How did you hear about us */}
                  <div>
                    <label htmlFor="howHeard" className="block text-sm font-medium text-gray-700 mb-2">
                      How did you hear about us? *
                    </label>
                    <select
                      id="howHeard"
                      name="howHeard"
                      value={formData.howHeard}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.howHeard ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">---- Select ----</option>
                      <option value="Career Fair">Career Fair</option>
                      <option value="Networking Event">Networking Event</option>
                      <option value="Job Board">Job Board (e.g., LinkedIn, Indeed, Glassdoor)</option>
                      <option value="Company Website">Company Website</option>
                      <option value="Employee Referral">Employee Referral</option>
                      <option value="Social Media">Social Media (e.g., Facebook, Twitter, Instagram)</option>
                      <option value="Recruitment Agency">Recruitment Agency</option>
                      <option value="Walk-in">Walk-in / Direct Application</option>
                    </select>
                    {errors.howHeard && <p className="text-red-500 text-sm mt-1">{errors.howHeard}</p>}
                  </div>
                </div>
              </div>

              {/* File Upload Section */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                  File Upload
                </h3>

                <div>
                  <label htmlFor="cv" className="block text-sm font-medium text-gray-700 mb-2">
                    File Upload *
                  </label>
                  <div className={`border-2 border-dashed rounded-lg p-6 text-center ${
                    errors.cv ? 'border-red-500 bg-red-50' : 'border-gray-300 hover:border-gray-400'
                  }`}>
                    <input
                      type="file"
                      id="cv"
                      name="cv"
                      onChange={handleFileChange}
                      accept=".pdf,.doc,.docx"
                      className="hidden"
                    />
                    <label htmlFor="cv" className="cursor-pointer">
                      <div className="space-y-2">
                        <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                          <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                        <div className="text-gray-600">
                          {cvFile ? (
                            <p className="font-medium text-blue-600">{cvFile.name}</p>
                          ) : (
                            <p>Click or drag a file to this area to upload.</p>
                          )}
                        </div>
                      </div>
                    </label>
                  </div>
                  {errors.cv && <p className="text-red-500 text-sm mt-1">{errors.cv}</p>}
                </div>
              </div>

              {/* Cover Letter */}
              <div className="space-y-6">
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Cover Letter/Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows="4"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Tell us why you're interested in this position..."
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="pt-6">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full py-3 px-6 rounded-md text-white font-medium transition-all duration-200 ${
                    isSubmitting
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500'
                  }`}
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Submitting...
                    </div>
                  ) : (
                    'Submit'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Floating Admin Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Link
          to={authAPI.isAuthenticated() ? "/admin" : "/admin/login"}
          className="bg-gradient-to-r from-gray-800 to-gray-900 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-110 group"
          title={authAPI.isAuthenticated() ? "Go to Admin Dashboard" : "Admin Login"}
        >
          <svg className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </Link>
      </div>
    </div>
  );
};

export default ApplicantForm;
